{"name": "dashboard", "version": "1.2.0", "license": "MIT", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "preinstall": "npx only-allow pnpm", "test:api": "node test-discord-api.js", "test:api:ts": "npx ts-node test-discord-api.ts", "test:discord": "node run-discord-tests.js", "test:discord:all": "npm run test:api && npm run test:api:ts", "test:whop": "npx ts-node test-whop-integration.ts", "deploy:frontend": "npm run build && npm start", "sevalla:build": "pnpm run build", "sevalla:start": "pnpm start", "sevalla:health": "curl -f http://localhost:3000/api/health || exit 1", "clear-cache": "node scripts/clear-cache.js", "clear-cache:next": "node scripts/clear-cache.js nextjs", "clear-cache:node": "node scripts/clear-cache.js node", "clear-cache:all": "node scripts/clear-cache.js all", "dev:reset": "bash scripts/dev-reset.sh", "dev:fresh": "npm run clear-cache && npm run dev", "debug:features": "node scripts/clear-cache.js debug"}, "private": true, "dependencies": {"@chakra-ui/anatomy": "^2.0.1", "@chakra-ui/form-control": "^2.0.0", "@chakra-ui/icon": "^3.0.0", "@chakra-ui/layout": "^2.0.0", "@chakra-ui/menu": "^2.1.8", "@chakra-ui/react": "^2.4.0", "@chakra-ui/spinner": "^2.0.0", "@chakra-ui/styled-system": "^2.0.0", "@chakra-ui/system": "^2.0.0", "@chakra-ui/theme-tools": "^2.0.0", "@emotion/react": "^11.8.1", "@emotion/styled": "^11.3.0", "@hookform/resolvers": "^2.9.11", "@tanstack/react-query": "^4.40.1", "@whop/api": "^0.0.36", "apexcharts": "^4.7.0", "chakra-react-select": "^4.4.2", "cookies-next": "^2.1.1", "deepmerge-ts": "^4.2.2", "framer-motion": "^6.0.0", "next": "^14.2.30", "react": "^18.2.0", "react-apexcharts": "^1.4.0", "react-calendar": "^3.7.0", "react-colorful": "^5.6.1", "react-dom": "^18.0.0", "react-dropzone": "^14.2.3", "react-hook-form": "^7.43.2", "react-icons": "^4.3.1", "zod": "^3.20.6", "zustand": "^4.4.6"}, "devDependencies": {"@babel/core": "^7.21.0", "@types/node": "18.18.10", "@types/react": "^18.0.0", "@types/react-calendar": "^3.5.2", "@types/react-dom": "^18.0.0", "@typescript-eslint/eslint-plugin": "^5.57.0", "@typescript-eslint/parser": "^5.57.0", "@whop-apps/dev-proxy": "^0.0.1-canary.117", "concurrently": "^7.6.0", "eslint": "^8.37.0", "eslint-config-next": "^13.2.1", "eslint-config-prettier": "^8.8.0", "eslint-plugin-prettier": "^4.2.1", "prettier": "^2.8.7", "ts-node": "^10.9.1", "typescript": "^5.3.2"}, "packageManager": "pnpm@10.12.4+sha512.5ea8b0deed94ed68691c9bad4c955492705c5eeb8a87ef86bc62c74a26b037b08ff9570f108b2e4dbd1dd1a9186fea925e527f141c648e85af45631074680184"}