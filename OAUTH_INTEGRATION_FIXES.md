# Discord OAuth2 Integration Fixes

## 🎯 Overview

This document outlines the fixes implemented to resolve Discord OAuth2 authorization URL issues, specifically addressing missing required fields like `redirect_uri` and improving the overall OAuth flow.

## 🔧 Issues Fixed

### 1. Missing `redirect_uri` Parameter
**Problem**: The original Discord OAuth2 authorization URL was missing the required `redirect_uri` parameter:
```
https://discord.com/oauth2/authorize?client_id=1394521471862308884&permissions=8&scope=bot&guild_id=1394355426941730856
```

**Solution**: Added proper `redirect_uri` parameter and other required fields:
```
https://discord.com/oauth2/authorize?client_id=1394521471862308884&permissions=8&scope=bot%20applications.commands&response_type=code&redirect_uri=https://your-domain.com/api/auth/bot-callback&guild_id=1394355426941730856
```

### 2. Inconsistent OAuth URL Generation
**Problem**: OAuth URLs were manually constructed in multiple places without consistency.

**Solution**: Created centralized utility functions for OAuth URL generation.

## 📁 Files Created/Modified

### New Files

#### 1. `/src/pages/api/auth/bot-invite.ts`
- **Purpose**: Handles bot invitation OAuth flow
- **Features**:
  - Generates proper Discord OAuth2 URLs with all required parameters
  - Supports both JSON API responses and direct redirects
  - Includes guild_id, permissions, and state parameters

#### 2. `/src/pages/api/auth/bot-callback.ts`
- **Purpose**: Processes Discord OAuth2 callbacks for bot invitations
- **Features**:
  - Exchanges authorization codes for access tokens
  - Handles guild-specific redirects
  - Comprehensive error handling with user-friendly error pages
  - Locale support through state parameter

#### 3. `/src/pages/auth/error.tsx`
- **Purpose**: User-friendly OAuth error page
- **Features**:
  - Displays authentication errors clearly
  - Provides retry and navigation options
  - Shows detailed error messages for debugging

#### 4. `/src/utils/discord-oauth.ts`
- **Purpose**: Centralized OAuth utility functions
- **Features**:
  - `generateBotInviteUrl()` - Creates bot invitation URLs
  - `generateUserAuthUrl()` - Creates user authentication URLs
  - `validateOAuthCallback()` - Validates OAuth callback parameters
  - `getOAuthRedirectBase()` - Gets environment-appropriate base URLs

### Modified Files

#### 1. `/src/config/common.tsx`
- **Changes**: Updated `inviteUrl` to include all required OAuth parameters
- **Before**: Basic bot scope only
- **After**: Includes `applications.commands` scope, `response_type`, and `redirect_uri`

#### 2. `/src/pages/api/auth/login.ts`
- **Changes**: Refactored to use centralized OAuth utility functions
- **Improvements**: Consistent URL generation and better logging

## 🚀 Usage Examples

### Bot Invitation with Guild Pre-selection
```typescript
// Generate a bot invite URL for a specific guild
const inviteUrl = generateBotInviteUrl({
  clientId: process.env.BOT_CLIENT_ID,
  permissions: '8', // Administrator
  guildId: '1394355426941730856',
  state: 'en' // Optional locale
});
```

### API Endpoint Usage
```bash
# Get bot invite URL as JSON
GET /api/auth/bot-invite?guild_id=1394355426941730856&permissions=8&state=en

# Direct redirect to Discord
GET /api/auth/bot-invite?guild_id=1394355426941730856&permissions=8
```

### User Authentication
```typescript
// Generate user auth URL
const authUrl = generateUserAuthUrl({
  clientId: process.env.BOT_CLIENT_ID,
  state: 'dashboard_redirect'
});
```

## 🔐 OAuth Flow Diagrams

### Bot Invitation Flow
```
1. User clicks "Add Bot" → /api/auth/bot-invite
2. Redirect to Discord OAuth → discord.com/oauth2/authorize
3. User authorizes bot → Discord redirects to /api/auth/bot-callback
4. Exchange code for token → Set user session
5. Redirect to guild dashboard → /guilds/{guild_id}
```

### User Authentication Flow
```
1. User clicks "Login" → /api/auth/login
2. Redirect to Discord OAuth → discord.com/oauth2/authorize
3. User authorizes app → Discord redirects to /api/auth/callback
4. Exchange code for token → Set user session
5. Redirect to dashboard → /user/home
```

## 🛡️ Security Improvements

### 1. Proper Redirect URI Validation
- All OAuth flows now include explicit `redirect_uri` parameters
- Redirect URIs are environment-aware (dev/prod)
- Prevents open redirect vulnerabilities

### 2. Enhanced Error Handling
- OAuth errors are properly caught and displayed
- User-friendly error pages with recovery options
- Detailed logging for debugging

### 3. State Parameter Usage
- Supports CSRF protection through state parameter
- Locale preservation across OAuth flow
- Custom redirect handling

## 🌐 Environment Configuration

### Required Environment Variables
```bash
# Bot credentials
BOT_CLIENT_ID="your_bot_client_id"
BOT_CLIENT_SECRET="your_bot_client_secret"
NEXT_PUBLIC_BOT_CLIENT_ID="your_bot_client_id"

# Application URLs
APP_URL="https://your-domain.com"  # Production
# APP_URL="http://localhost:3000"   # Development
```

### Discord Application Settings
Ensure your Discord application has these redirect URIs configured:
- `https://your-domain.com/api/auth/callback` (user auth)
- `https://your-domain.com/api/auth/bot-callback` (bot invite)
- `http://localhost:3000/api/auth/callback` (dev - user auth)
- `http://localhost:3000/api/auth/bot-callback` (dev - bot invite)

## 🧪 Testing

### Manual Testing
1. **Bot Invitation**: Visit `/api/auth/bot-invite?guild_id=YOUR_GUILD_ID`
2. **User Login**: Visit `/api/auth/login`
3. **Error Handling**: Visit `/auth/error?message=Test%20Error`

### Integration Testing
```typescript
// Test OAuth URL generation
const botUrl = generateBotInviteUrl({
  clientId: 'test_client_id',
  guildId: 'test_guild_id'
});

console.log(botUrl);
// Expected: https://discord.com/api/oauth2/authorize?client_id=test_client_id&permissions=8&scope=bot%20applications.commands&response_type=code&redirect_uri=http%3A%2F%2Flocalhost%3A3000%2Fapi%2Fauth%2Fbot-callback&guild_id=test_guild_id
```

## 📋 Checklist for Deployment

- [ ] Update Discord application redirect URIs
- [ ] Set proper `APP_URL` environment variable
- [ ] Test bot invitation flow
- [ ] Test user authentication flow
- [ ] Verify error handling works
- [ ] Check OAuth URLs include all required parameters
- [ ] Validate redirect URIs match Discord app settings

## 🔄 Migration Notes

### From Old Implementation
1. **Bot Invites**: Replace direct Discord OAuth URLs with `/api/auth/bot-invite`
2. **URL Generation**: Use utility functions instead of manual string construction
3. **Error Handling**: Update error flows to use new error page

### Breaking Changes
- Bot invitation URLs now require proper callback handling
- OAuth flows now include additional scopes (`applications.commands`)
- Error responses redirect to error page instead of returning JSON

## 🐛 Troubleshooting

### Common Issues

#### "Invalid Redirect URI" Error
- **Cause**: Redirect URI not configured in Discord application
- **Solution**: Add the callback URLs to your Discord app settings

#### "Missing Code Parameter" Error
- **Cause**: User denied authorization or OAuth flow interrupted
- **Solution**: Check error page for details, retry authorization

#### "Token Exchange Failed" Error
- **Cause**: Invalid client credentials or redirect URI mismatch
- **Solution**: Verify `BOT_CLIENT_ID`, `BOT_CLIENT_SECRET`, and `APP_URL`

### Debug Logging
Enable debug logging by checking browser console and server logs:
```bash
# Server logs show OAuth URL generation
console.log('Generated bot invite URL:', authUrl);

# Browser network tab shows OAuth redirects
```

---

**Note**: This implementation ensures full compliance with Discord's OAuth2 specification and provides a robust, secure authentication flow for both user login and bot invitation scenarios.