# Admin Login Setup Guide

This guide explains how to set up admin login capabilities for the Discord bot frontend.

## Overview

The admin login system extends the existing Discord OAuth2 authentication to support admin privileges. Admin users are identified by their Discord user IDs and have access to additional features like:

- AI Agents Management
- Analytics Dashboard  
- Session Management
- System Administration

## Setup Instructions

### 1. Configure Admin Users

Add admin user Discord IDs to your environment variables:

```bash
# In your .env file
ADMIN_USER_IDS=123456789012345678,987654321098765432
```

To find a Discord user ID:
1. Enable Developer Mode in Discord (User Settings > Advanced > Developer Mode)
2. Right-click the user and select "Copy ID"

### 2. Environment Variables

Copy `.env.example` to `.env` and configure:

```bash
# Discord Bot Configuration
BOT_CLIENT_ID=your_discord_bot_client_id
BOT_CLIENT_SECRET=your_discord_bot_client_secret

# Admin User Configuration
ADMIN_USER_IDS=123456789012345678,987654321098765432

# Optional configurations
COOKIE_DOMAIN=.yourdomain.com
NEXT_PUBLIC_API_ENDPOINT=https://your-frontend-domain.com
APP_URL=https://your-frontend-domain.com
```

### 3. API Endpoints

The following new endpoints have been added:

- `GET /api/auth/me` - Get current user profile with admin status
- `GET /api/admin/sessions` - Verify admin privileges  
- `POST /api/admin/sessions` - Admin operations (cleanup, etc.)

### 4. Frontend Components

New components for admin functionality:

- `AdminAuthGuard` - Protects admin-only routes
- Enhanced `useUserProfile()` hook - Provides user profile with admin status
- `useIsAdmin()` hook - Quick admin status check

## Usage

### Protecting Admin Routes

Wrap admin pages with `AdminAuthGuard`:

```tsx
import { AdminAuthGuard } from '@/components/auth/AdminAuthGuard';

export default function AdminPage() {
  return (
    <AdminAuthGuard>
      <YourAdminContent />
    </AdminAuthGuard>
  );
}
```

### Checking Admin Status

Use the `useIsAdmin()` hook in components:

```tsx
import { useIsAdmin } from '@/utils/auth/hooks';

function MyComponent() {
  const isAdmin = useIsAdmin();
  
  return (
    <div>
      {isAdmin && <AdminButton />}
    </div>
  );
}
```

### Getting User Profile

Use `useUserProfile()` for detailed user information:

```tsx
import { useUserProfile } from '@/utils/auth/hooks';

function UserInfo() {
  const { profile, status } = useUserProfile();
  
  if (status === 'loading') return <div>Loading...</div>;
  if (status === 'unauthenticated') return <div>Not logged in</div>;
  
  return (
    <div>
      <p>Welcome, {profile.username}#{profile.discriminator}</p>
      {profile.isAdmin && <p>You have admin privileges</p>}
    </div>
  );
}
```

## Security Considerations

1. **Server-Side Verification**: All admin checks are verified server-side using Discord API
2. **Admin User IDs**: Store admin user IDs securely (environment variables or database)
3. **Session Management**: Admin sessions use the same secure cookie system as regular users
4. **Audit Logging**: All admin actions should be logged for security purposes
5. **Rate Limiting**: Consider implementing rate limiting for admin endpoints

## Testing

1. Set your Discord user ID in `ADMIN_USER_IDS`
2. Log in through Discord OAuth
3. Navigate to `/admin/dashboard` 
4. Verify you have admin access and can see admin features

## Troubleshooting

### User not recognized as admin
- Check that your Discord user ID is correctly added to `ADMIN_USER_IDS`
- Ensure the environment variable is loaded (restart your application)
- Check browser console for authentication errors

### Admin dashboard shows "Access Denied"
- Verify your Discord OAuth tokens are valid
- Check that `/api/auth/me` returns your profile with `isAdmin: true`
- Ensure cookies are being set correctly

### Discord API errors
- Verify `BOT_CLIENT_ID` and `BOT_CLIENT_SECRET` are correct
- Check that your Discord application has the correct OAuth2 redirect URLs configured
- Ensure the bot has appropriate permissions

## Production Deployment

1. Use environment variables or secure configuration management for admin user IDs
2. Consider implementing database-based admin management for larger teams
3. Set up proper logging and monitoring for admin actions
4. Use HTTPS in production for secure cookie transmission
5. Consider implementing 2FA for admin accounts

## Future Enhancements

- Database-based admin user management
- Role-based permissions (super admin, moderator, etc.)
- Admin action audit logging
- 2FA for admin accounts
- Admin session timeout configuration