# Backend Error Handling Improvements

## J<PERSON><PERSON> Parsing Error Fix

The "No number after minus sign in JSON" error is occurring in the backend's session parsing. Here's the fix needed for the **backend branch**:

## File: `lib/auth-utils.ts`

Update the `getServerSession()` function with better error handling:

```typescript
// Get server session from cookie with expiration checking
export function getServerSession(req: NextRequest): AccessToken | null {
  try {
    const tokenCookie = req.cookies.get('discord_token')?.value;
    if (!tokenCookie) {
      console.log('No discord_token cookie found');
      return null;
    }

    // Add validation before parsing JSON
    if (!tokenCookie.trim() || tokenCookie.trim().length === 0) {
      console.warn('Empty or whitespace-only token cookie');
      return null;
    }

    // Check for obviously malformed JSON (starts with invalid characters)
    const trimmed = tokenCookie.trim();
    if (!trimmed.startsWith('{') && !trimmed.startsWith('[') && !trimmed.startsWith('"')) {
      console.warn('Token cookie does not appear to be valid JSON:', trimmed.substring(0, 50));
      return null;
    }

    let token;
    try {
      token = JSON.parse(tokenCookie);
    } catch (parseError) {
      console.error('Failed to parse token cookie as JSON:', {
        error: parseError instanceof Error ? parseError.message : 'Unknown error',
        cookieLength: tokenCookie.length,
        cookiePreview: tokenCookie.substring(0, 100) + (tokenCookie.length > 100 ? '...' : '')
      });
      return null;
    }
    
    // Basic validation of token structure
    if (!token || typeof token !== 'object') {
      console.warn('Token is not an object:', typeof token);
      return null;
    }

    if (!token.access_token || !token.token_type || !token.expires_in) {
      console.warn('Invalid token structure - missing required fields:', {
        hasAccessToken: !!token.access_token,
        hasTokenType: !!token.token_type,
        hasExpiresIn: !!token.expires_in
      });
      return null;
    }

    // Check if token has an issue date and calculate if it's expired
    if (token.issued_at) {
      const currentTime = Math.floor(Date.now() / 1000);
      const tokenAge = currentTime - token.issued_at;
      const isExpired = tokenAge >= token.expires_in;
      
      console.log(`Token age: ${tokenAge}s, expires after: ${token.expires_in}s, expired: ${isExpired}`);
      
      if (isExpired) {
        console.warn('Discord token has expired');
        return null;
      }
    } else {
      console.warn('Token missing issued_at timestamp - cannot verify expiration');
    }

    console.log('Valid session token found');
    return token;
  } catch (error) {
    console.error('Unexpected error in getServerSession:', {
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined
    });
    return null;
  }
}
```

## Additional Improvements

### 1. Add Response Validation

For API routes that call `JSON.parse()`, add validation:

```typescript
// In discord-utils.ts, update getUserGuilds function
export async function getUserGuilds(accessToken: string): Promise<Array<{
  id: string;
  name: string;
  permissions: string;
  owner: boolean;
}>> {
  const res = await discordApiRequest(`${API_ENDPOINT}/users/@me/guilds`, accessToken);
  
  if (!res.ok) {
    console.error(`Discord API error: ${res.status} ${res.statusText}`);
    
    // Get response text for better debugging
    let errorText = 'Unknown error';
    try {
      errorText = await res.text();
    } catch (textError) {
      console.error('Failed to read error response text:', textError);
    }
    
    console.error(`Discord API response: ${errorText}`);
    
    // Return more specific error types for better handling
    if (res.status === 429) {
      throw new Error(`Rate limited by Discord API: ${res.status} ${res.statusText}`);
    } else if (res.status === 401) {
      throw new Error(`Authentication failed: ${res.status} ${res.statusText}`);
    } else if (res.status === 403) {
      throw new Error(`Permission denied: ${res.status} ${res.statusText}`);
    }
    
    throw new Error(`Failed to get user guilds: ${res.status} ${res.statusText}`);
  }

  // Validate response before parsing
  const contentType = res.headers.get('content-type');
  if (!contentType || !contentType.includes('application/json')) {
    console.warn('Discord API returned non-JSON response:', contentType);
    throw new Error('Discord API returned invalid response format');
  }

  try {
    return await res.json();
  } catch (jsonError) {
    console.error('Failed to parse Discord API JSON response:', jsonError);
    throw new Error('Discord API returned invalid JSON');
  }
}
```

### 2. Error Response Standardization

Update `guild-utils.ts` to handle JSON parsing errors:

```typescript
// Standard error response with JSON parsing protection
export function errorResponse(message: string, status: number = 500) {
  // Ensure message is always a string and safe for JSON
  const safeMessage = typeof message === 'string' ? message : 'Internal server error';
  
  try {
    return NextResponse.json({ error: safeMessage }, { status });
  } catch (jsonError) {
    console.error('Failed to create JSON error response:', jsonError);
    // Fallback to plain text response
    return new NextResponse(safeMessage, { 
      status, 
      headers: { 'Content-Type': 'text/plain' } 
    });
  }
}
```

## Deployment Steps

1. **Apply these changes to the backend branch**
2. **Redeploy the backend service**
3. **Test the health endpoint**: `curl https://discordbot-energex-backend-nqzv2.sevalla.app/api/auth/health`
4. **Monitor logs** for improved error messages

These improvements will prevent JSON parsing crashes and provide better debugging information when issues occur.