# Backend-Frontend Split Architecture Deployment Guide

## Overview

Your Discord bot project uses a **split architecture** with separate frontend and backend deployments:

- **Frontend Branch**: React/Next.js dashboard UI
- **Backend Branch**: Next.js API server with Discord bot logic

## Current Production URLs

- **Frontend**: `https://discordbot-energex-jkhvk.sevalla.app`
- **Backend**: `https://discordbot-energex-backend-nqzv2.sevalla.app`

## Critical Environment Variables

### Frontend Environment Variables (Sevalla Dashboard)

Set these in your **frontend** deployment environment:

```env
# Discord OAuth Configuration
BOT_CLIENT_ID="1394521471862308884"
BOT_CLIENT_SECRET="[YOUR_ACTUAL_SECRET]"
NEXT_PUBLIC_BOT_CLIENT_ID="1394521471862308884"

# URLs
APP_URL="https://discordbot-energex-jkhvk.sevalla.app"
NEXT_PUBLIC_API_ENDPOINT="https://discordbot-energex-backend-nqzv2.sevalla.app"
INTERNAL_API_ENDPOINT="https://discordbot-energex-backend-nqzv2.sevalla.app"

# Production Settings
NODE_ENV="production"
PORT="3000"
ENABLE_ENV_LOGIN="false"
NEXT_TELEMETRY_DISABLED="1"
```

### Backend Environment Variables (Sevalla Dashboard)

Set these in your **backend** deployment environment:

```env
# Discord Configuration
DISCORD_CLIENT_ID="1394521471862308884"
DISCORD_CLIENT_SECRET="[YOUR_ACTUAL_SECRET]"
DISCORD_TOKEN="[YOUR_BOT_TOKEN]"

# URLs
WEB_URL="https://discordbot-energex-jkhvk.sevalla.app"

# Database (if applicable)
DATABASE_URL="[YOUR_DATABASE_URL]"

# Production Settings
NODE_ENV="production"
PORT="8080"
BRANCH_NAME="backend"
```

## Deployment Steps

### Step 1: Deploy Backend Service

1. **Switch to backend branch**:
   ```bash
   git checkout backend
   ```

2. **Deploy to Sevalla** at `https://discordbot-energex-backend-nqzv2.sevalla.app`

3. **Set backend environment variables** (listed above)

4. **Verify backend health**:
   ```bash
   curl https://discordbot-energex-backend-nqzv2.sevalla.app/api/auth/health
   ```

### Step 2: Deploy Frontend Service

1. **Switch to frontend branch**:
   ```bash
   git checkout frontend
   ```

2. **Deploy to Sevalla** at `https://discordbot-energex-jkhvk.sevalla.app`

3. **Set frontend environment variables** (listed above)

4. **Verify frontend loads** and can reach backend

### Step 3: Update Discord OAuth Settings

In Discord Developer Portal → OAuth2 → General, add these redirect URIs:

```
https://discordbot-energex-jkhvk.sevalla.app/api/auth/callback
https://discordbot-energex-backend-nqzv2.sevalla.app/api/auth/callback
```

## Architecture Flow

```
User → Frontend (jkhvk.sevalla.app) → Backend (backend-nqzv2.sevalla.app) → Discord API
```

1. **User** accesses the dashboard at the frontend URL
2. **Frontend** serves the React UI and handles OAuth redirects
3. **Frontend** makes API calls to the backend for data/features
4. **Backend** handles Discord API calls, bot logic, and database operations

## Common Issues & Solutions

### JSON Parsing Errors

**Cause**: Frontend trying to call wrong backend URL
**Fix**: Ensure `NEXT_PUBLIC_API_ENDPOINT` points to `https://discordbot-energex-backend-nqzv2.sevalla.app`

### CORS Errors

**Cause**: Backend not allowing requests from frontend domain
**Fix**: Backend should allow requests from `https://discordbot-energex-jkhvk.sevalla.app`

### Authentication Issues

**Cause**: Mismatched OAuth redirect URIs
**Fix**: Ensure Discord app has both frontend and backend redirect URIs

### 404 Errors on Features

**Cause**: API routes don't exist or wrong environment variables
**Fix**: Verify backend deployment and environment configuration

## Testing Checklist

- [ ] Backend health check responds: `curl https://discordbot-energex-backend-nqzv2.sevalla.app/api/auth/health`
- [ ] Frontend loads without errors
- [ ] OAuth login flow works end-to-end
- [ ] AI agents feature loads without JSON parsing errors
- [ ] All feature APIs respond correctly
- [ ] No CORS errors in browser console

## Important Notes

⚠️ **Never deploy both branches to the same URL** - they have conflicting API routes

✅ **Always deploy backend first** - frontend depends on backend being available

🔄 **Update environment variables** when URLs change

💾 **Backend handles all data persistence** - frontend is stateless UI only