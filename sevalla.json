{"name": "dashboard", "version": "1.2.0", "build": {"command": "pnpm run build", "path": ".", "cache": true}, "deployment": {"type": "application", "environment": "nodejs", "port": 3000, "healthCheck": {"path": "/api/health", "timeout": 30, "interval": 10, "retries": 3}, "autoDeployment": true, "zeroDowntime": true}, "processes": {"web": {"command": "pnpm start", "healthCheck": "/api/health"}}, "pipeline": {"type": "trunk-based", "stages": [{"name": "development", "branch": "develop", "autoDeployment": true}, {"name": "staging", "branch": "staging", "autoDeployment": true}, {"name": "production", "branch": "master", "autoDeployment": false}]}, "features": {"previewApps": true, "edgeCaching": true, "cdn": true}}