# Discord OAuth Redirect URI Configuration Fix

## Problem
The Discord OAuth URL is showing "invalid redirect url" error because the redirect URI `http://localhost:3000/api/auth/bot-callback` is not configured in the Discord Developer Portal.

## Root Cause
The Discord application settings in the Developer Portal don't include the required redirect URIs for local development.

## Solution

### Step 1: Access Discord Developer Portal
1. Go to https://discord.com/developers/applications
2. Log in with your Discord account
3. Select your application (Client ID: `1394521471862308884`)

### Step 2: Configure OAuth2 Redirect URIs
1. Navigate to **OAuth2** → **General** in the left sidebar
2. In the **Redirects** section, add the following URIs:
   
   **For Local Development:**
   ```
   http://localhost:3000/api/auth/callback
   http://localhost:3000/api/auth/bot-callback
   ```
   
   **For Production (Sevalla):**
   ```
   https://discordbot-energex-jkhvk.sevalla.app/api/auth/callback
   https://discordbot-energex-jkhvk.sevalla.app/api/auth/bot-callback
   ```
3. Click **Save Changes**

### Step 3: Verify Current Configuration
Your current OAuth URL structure:
```
https://discord.com/oauth2/authorize?client_id=1394521471862308884&permissions=8&scope=bot%20applications.commands&response_type=code&redirect_uri=http://localhost:3000/api/auth/bot-callback&guild_id=1394355426941730856
```

### Step 4: Production Configuration
For your Sevalla deployment, ensure these environment variables are set:

**Update `.env.sevalla`:**
```env
APP_URL="https://discordbot-energex-jkhvk.sevalla.app"
NEXT_PUBLIC_API_ENDPOINT="https://your-backend-name.sevalla.app"
```

**Production OAuth URLs will be:**
```
https://discordbot-energex-jkhvk.sevalla.app/api/auth/callback
https://discordbot-energex-jkhvk.sevalla.app/api/auth/bot-callback
```

## Environment Variables
Ensure your `.env` file (create from `.env.example`) contains:
```env
BOT_CLIENT_ID="1394521471862308884"
BOT_CLIENT_SECRET="<YOUR_BOT_CLIENT_SECRET>"
NEXT_PUBLIC_BOT_CLIENT_ID="1394521471862308884"
APP_URL="http://localhost:3000"
```

## Testing
After configuring the redirect URIs:
1. Restart your development server: `pnpm run start`
2. Test the OAuth flow with your bot invite URL
3. Verify successful redirect to the callback endpoint

## Common Issues
- **Case sensitivity**: Ensure URLs match exactly (including http vs https)
- **Trailing slashes**: Don't include trailing slashes in redirect URIs
- **Port numbers**: Include the correct port (3000 for local development)
- **Protocol**: Use `http://` for localhost, `https://` for production

## Security Notes
- Never commit your actual `.env` file with real secrets
- Use different client secrets for development and production
- Regularly rotate your client secrets
- Only add trusted domains to redirect URIs