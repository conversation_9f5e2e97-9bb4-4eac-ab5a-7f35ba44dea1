const path = require('path');

/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,
  pageExtensions: ['ts', 'tsx', 'js', 'jsx'],
  experimental: {
    externalDir: true
  },
  webpack: (config) => {
    config.resolve.alias['@'] = path.resolve(__dirname, 'src');
    return config;
  },
  async redirects() {
    return [
      { source: '/auth', destination: '/auth/signin', permanent: false },
      { source: '/user', destination: '/user/home', permanent: false },
      { source: '/admin', destination: '/admin/dashboard', permanent: false },
      { source: '/', destination: '/user/home', permanent: false },
    ];
  },
  async headers() {
    return [
      {
        source: '/:path*.(png|jpg|jpeg|gif|svg|ico|webp)',
        headers: [
          {
            key: 'Cache-Control',
            value: 'public, max-age=86400',
          },
        ],
      },
    ];
  },
  i18n: {
    locales: ['en', 'cn'],
    defaultLocale: 'en',
  },
  // Sevalla deployment optimizations
  poweredByHeader: false,
  generateEtags: false,
  compress: true,
};

module.exports = nextConfig;
