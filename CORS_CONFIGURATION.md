# CORS Configuration for Backend Service

## Overview

Since your frontend (`https://discordbot-energex-jkhvk.sevalla.app`) and backend (`https://discordbot-energex-backend-nqzv2.sevalla.app`) are on different domains, you need to configure CORS (Cross-Origin Resource Sharing) headers.

## Backend CORS Configuration

Add this to your **backend branch** in `middleware.ts` or create a new CORS middleware:

```typescript
import { NextRequest, NextResponse } from 'next/server';

export function middleware(request: NextRequest) {
  // Handle CORS for API routes
  if (request.nextUrl.pathname.startsWith('/api/')) {
    const response = NextResponse.next();
    
    // Allow requests from frontend domain
    const allowedOrigins = [
      'https://discordbot-energex-jkhvk.sevalla.app',
      'http://localhost:3000', // For development
      'http://localhost:8080'  // For local backend testing
    ];
    
    const origin = request.headers.get('origin');
    
    if (origin && allowedOrigins.includes(origin)) {
      response.headers.set('Access-Control-Allow-Origin', origin);
    }
    
    // Handle preflight requests
    if (request.method === 'OPTIONS') {
      response.headers.set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, PATCH, OPTIONS');
      response.headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization, x-user-session');
      response.headers.set('Access-Control-Max-Age', '86400'); // 24 hours
      return new Response(null, { status: 200, headers: response.headers });
    }
    
    // Allow credentials for auth cookies
    response.headers.set('Access-Control-Allow-Credentials', 'true');
    
    return response;
  }
  
  return NextResponse.next();
}

export const config = {
  matcher: '/api/:path*'
};
```

## Alternative: Next.js Config Method

Add to `next.config.js` in the **backend branch**:

```javascript
/** @type {import('next').NextConfig} */
const nextConfig = {
  async headers() {
    return [
      {
        source: '/api/:path*',
        headers: [
          {
            key: 'Access-Control-Allow-Origin',
            value: 'https://discordbot-energex-jkhvk.sevalla.app'
          },
          {
            key: 'Access-Control-Allow-Methods',
            value: 'GET, POST, PUT, DELETE, PATCH, OPTIONS'
          },
          {
            key: 'Access-Control-Allow-Headers',
            value: 'Content-Type, Authorization, x-user-session'
          },
          {
            key: 'Access-Control-Allow-Credentials',
            value: 'true'
          }
        ]
      }
    ];
  }
};

module.exports = nextConfig;
```

## Frontend Request Configuration

Ensure your frontend requests include credentials:

```typescript
// In src/utils/fetch/requests.ts
export function botRequest<T extends Options>(session: AccessToken, options: T): T {
  return {
    ...options,
    origin: bot_api_endpoint,
    request: deepmerge(
      {
        headers: {
          Authorization: `${session.token_type} ${session.access_token}`,
        },
        credentials: 'include', // Important for CORS with cookies
        mode: 'cors',
      },
      options.request
    ),
  };
}
```

## Testing CORS Configuration

1. **Check preflight requests** in browser dev tools
2. **Verify headers** in Network tab
3. **Test with curl**:
   ```bash
   # Test preflight
   curl -X OPTIONS \
     -H "Origin: https://discordbot-energex-jkhvk.sevalla.app" \
     -H "Access-Control-Request-Method: POST" \
     -H "Access-Control-Request-Headers: Content-Type, Authorization" \
     https://discordbot-energex-backend-nqzv2.sevalla.app/api/guilds/test/features/ai-agents
   
   # Test actual request
   curl -X GET \
     -H "Origin: https://discordbot-energex-jkhvk.sevalla.app" \
     https://discordbot-energex-backend-nqzv2.sevalla.app/api/auth/health
   ```

## Common CORS Issues

### Issue: Requests blocked by CORS policy
**Solution**: Ensure backend allows the frontend origin

### Issue: Cookies not sent with requests
**Solution**: Set `credentials: 'include'` and `Access-Control-Allow-Credentials: true`

### Issue: Custom headers blocked
**Solution**: Add headers to `Access-Control-Allow-Headers`

### Issue: Preflight failures
**Solution**: Handle OPTIONS method in backend routes

## Security Considerations

1. **Only allow specific origins** - don't use wildcards (`*`) in production
2. **Limit allowed methods** to what you actually need
3. **Set appropriate cache times** for preflight responses
4. **Use HTTPS only** in production

## Development vs Production

```typescript
const allowedOrigins = process.env.NODE_ENV === 'development' 
  ? ['http://localhost:3000', 'http://localhost:8080']
  : ['https://discordbot-energex-jkhvk.sevalla.app'];
```

This ensures you can develop locally while restricting production access.