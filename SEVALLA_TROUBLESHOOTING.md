# Sevalla Deployment Troubleshooting Guide

## Current Issue
JSON parsing errors and CORS issues between frontend and backend services.

## Architecture Overview
- **Frontend**: `https://discordbot-energex-jkhvk.sevalla.app` (frontend branch)
- **Backend**: `https://discordbot-energex-backend-nqzv2.sevalla.app` (backend branch)

## Root Causes & Solutions

### 1. Split Architecture Misconfiguration
**Problem**: Frontend and backend are deployed separately but environment variables don't match.

**Solution**: 

**Frontend Environment Variables** (for `https://discordbot-energex-jkhvk.sevalla.app`):
```env
BOT_CLIENT_ID="1394521471862308884"
BOT_CLIENT_SECRET="[YOUR_ACTUAL_SECRET]"
NEXT_PUBLIC_BOT_CLIENT_ID="1394521471862308884"
APP_URL="https://discordbot-energex-jkhvk.sevalla.app"
NEXT_PUBLIC_API_ENDPOINT="https://discordbot-energex-backend-nqzv2.sevalla.app"
INTERNAL_API_ENDPOINT="https://discordbot-energex-backend-nqzv2.sevalla.app"
NODE_ENV="production"
PORT="3000"
ENABLE_ENV_LOGIN="false"
NEXT_TELEMETRY_DISABLED="1"
```

**Backend Environment Variables** (for `https://discordbot-energex-backend-nqzv2.sevalla.app`):
```env
DISCORD_CLIENT_ID="1394521471862308884"
DISCORD_CLIENT_SECRET="[YOUR_ACTUAL_SECRET]"
DISCORD_TOKEN="[YOUR_BOT_TOKEN]"
WEB_URL="https://discordbot-energex-jkhvk.sevalla.app"
NODE_ENV="production"
PORT="8080"
BRANCH_NAME="backend"
```

### 2. Discord OAuth Redirect URIs Missing
**Problem**: Discord application doesn't have production redirect URIs for both services.

**Solution**: Add these to Discord Developer Portal → OAuth2 → General:
```
https://discordbot-energex-jkhvk.sevalla.app/api/auth/callback
https://discordbot-energex-backend-nqzv2.sevalla.app/api/auth/callback
```

### 3. JSON Parsing Errors
**Problem**: Frontend trying to parse HTML error responses as JSON.

**Solution**: 
1. **Deploy backend branch** to `https://discordbot-energex-backend-nqzv2.sevalla.app`
2. **Deploy frontend branch** to `https://discordbot-energex-jkhvk.sevalla.app`
3. **Remove conflicting API routes** from frontend (already fixed)
4. **Test backend health**: `curl https://discordbot-energex-backend-nqzv2.sevalla.app/api/auth/health`

### 4. Build/Deployment Issues
**Problem**: Application might not be building or deploying correctly.

**Solution**: Check Sevalla deployment logs for:
- Build errors
- Runtime errors
- Missing dependencies
- Port configuration issues

## Step-by-Step Fix Process

### Step 1: Update Environment Variables
1. Go to your Sevalla dashboard
2. Navigate to your application settings
3. Set all environment variables listed above
4. Replace `[YOUR_ACTUAL_SECRET]` with your real Discord bot client secret

### Step 2: Update Discord Application
1. Go to https://discord.com/developers/applications
2. Select your application (Client ID: 1394521471862308884)
3. Navigate to OAuth2 → General
4. Add the production redirect URIs
5. Save changes

### Step 3: Verify Backend Deployment
1. Check if your bot backend is deployed separately
2. Verify it's accessible at the NEXT_PUBLIC_API_ENDPOINT URL
3. Test API endpoints manually

### Step 4: Redeploy Application
1. Trigger a new deployment on Sevalla
2. Monitor deployment logs for errors
3. Check application startup logs

### Step 5: Test OAuth Flow
1. Visit your production URL
2. Try the Discord login/bot invite flow
3. Check browser developer tools for errors

## Common Issues & Solutions

### "Invalid query param" Error
- **Cause**: Missing or incorrect redirect URIs in Discord settings
- **Fix**: Add production redirect URIs to Discord Developer Portal

### Application Won't Start
- **Cause**: Missing environment variables or build errors
- **Fix**: Check Sevalla logs and ensure all env vars are set

### 404 Errors on API Routes
- **Cause**: Backend service not running or incorrect API endpoint
- **Fix**: Verify backend deployment and API_ENDPOINT configuration

### OAuth Redirect Loops
- **Cause**: Mismatched redirect URIs between Discord and application
- **Fix**: Ensure exact URL match in Discord settings

## Verification Checklist

- [ ] All environment variables set in Sevalla dashboard
- [ ] Discord redirect URIs include production URLs
- [ ] Backend service deployed and accessible
- [ ] Application builds successfully
- [ ] No errors in Sevalla deployment logs
- [ ] OAuth flow works end-to-end

## Next Steps

1. **Immediate**: Set environment variables in Sevalla dashboard
2. **Critical**: Add production redirect URIs to Discord
3. **Important**: Verify backend service deployment
4. **Testing**: Test complete OAuth flow

## Getting Help

If issues persist:
1. Check Sevalla deployment logs
2. Test API endpoints manually
3. Verify Discord application configuration
4. Check browser developer tools for client-side errors